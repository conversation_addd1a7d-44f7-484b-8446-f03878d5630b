<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端管理系统</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* CSS 变量用于动态视口高度 */
        :root {
            --vh: 1vh;
        }

        /* 应用布局样式 */
        .app-layout {
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
        }

        /* 移动端视口高度修复 */
        @supports (-webkit-touch-callout: none) {
            .app-layout {
                height: -webkit-fill-available;
                min-height: -webkit-fill-available;
            }
        }

        /* 使用 CSS 变量的动态高度 */
        @media (max-width: 768px) {
            .app-layout {
                height: calc(var(--vh, 1vh) * 100);
                min-height: calc(var(--vh, 1vh) * 100);
            }
        }

        .app-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--white);
            flex-shrink: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .app-sidebar {
            width: 240px;
            background-color: var(--bg-dark);
            color: var(--white);
            flex-shrink: 0;
            border-right: 1px solid var(--border-color);
        }

        .sidebar-nav {
            padding: var(--spacing-md) 0;
        }

        .nav-link {
            display: block;
            padding: var(--spacing-md) var(--spacing-lg);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition-colors);
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
        }

        .nav-link.active {
            background-color: rgba(0, 123, 255, 0.2);
            color: var(--white);
            border-left-color: var(--primary-color);
        }

        .content-frame {
            flex: 1;
            border: none;
            background-color: var(--white);
            width: 100%;
            height: 100%;
        }

        /* iframe 容器样式 */
        .iframe-container {
            flex: 1;
            position: relative;
            overflow: hidden;
            min-height: 0; /* 重要：允许 flex 子项收缩 */
            background-color: var(--gray-50);
        }

        /* iframe 加载指示器 */
        .iframe-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
            z-index: 1;
            color: var(--text-muted);
        }

        .iframe-loading.hidden {
            display: none;
        }

        .user-menu {
            gap: var(--spacing-sm);
        }

        /* 改善用户菜单按钮的可读性 */
        .user-menu .btn {
            background-color: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: var(--white);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            min-width: 80px;
            transition: all var(--transition-base);
        }

        .user-menu .btn:hover {
            background-color: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.7);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .user-menu .btn:focus {
            outline: 3px solid rgba(255, 255, 255, 0.5);
            outline-offset: 2px;
        }

        .user-menu .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: var(--white);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .user-menu .btn-danger:hover {
            background-color: var(--danger-hover);
            border-color: var(--danger-hover);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }


        /* 模态框样式优化 */
        .modal-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: var(--font-size-lg);
        }

        .modal-body .form-label {
            color: var(--text-primary);
            font-weight: 600;
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-sm);
        }

        .modal-body .form-control {
            border: 2px solid var(--border-color);
            color: var(--text-primary);
            font-size: var(--font-size-base);
            font-weight: 500;
            background-color: var(--white);
        }

        .modal-body .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            color: var(--text-primary);
        }

        .modal-body .form-control::placeholder {
            color: var(--text-muted);
            font-weight: normal;
        }

        .modal-body .form-text {
            color: var(--text-muted);
            font-size: var(--font-size-xs);
            margin-top: var(--spacing-xs);
            font-weight: 500;
        }

        .modal-footer .btn {
            font-weight: 600;
            min-width: 80px;
        }

        .modal-footer .btn-secondary {
            background-color: var(--gray-500);
            border-color: var(--gray-500);
            color: var(--white);
        }

        .modal-footer .btn-secondary:hover {
            background-color: var(--gray-600);
            border-color: var(--gray-600);
        }

        .modal-footer .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white);
        }

        .modal-footer .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        /* 错误和成功消息样式 */
        #passwordMessage .alert,
        #usernameMessage .alert {
            margin-top: var(--spacing-md);
            font-weight: 500;
            border-radius: var(--border-radius-base);
        }

        #passwordMessage .alert-danger,
        #usernameMessage .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c2c7;
            color: #842029;
            border: 1px solid #f5c2c7;
        }

        #passwordMessage .alert-success,
        #usernameMessage .alert-success {
            background-color: #d1e7dd;
            border-color: #badbcc;
            color: #0f5132;
            border: 1px solid #badbcc;
        }

        /* 移动端响应式 */
        @media (max-width: 768px) {
            .app-layout {
                height: 100vh;
                height: 100dvh; /* 动态视口高度 */
                overflow: hidden;
            }

            .app-sidebar {
                width: 100%;
                order: 2;
                flex-shrink: 0;
            }

            .sidebar-nav {
                padding: var(--spacing-sm) 0;
            }

            .sidebar-nav ul {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: var(--spacing-xs);
            }

            .nav-link {
                padding: var(--spacing-sm) var(--spacing-md);
                border-left: none;
                border-bottom: 3px solid transparent;
                border-radius: var(--border-radius-base);
            }

            .nav-link.active {
                border-left: none;
                border-bottom-color: var(--primary-color);
            }

            .app-header {
                flex-direction: column;
                gap: var(--spacing-md);
                text-align: center;
                flex-shrink: 0;
            }

            /* 移动端主内容区域 */
            .main-content-area {
                flex: 1;
                display: flex;
                flex-direction: column;
                min-height: 0;
                overflow: hidden;
            }

            .iframe-container {
                flex: 1;
                min-height: 0;
                position: relative;
            }

            .content-frame {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
            }

            .user-menu {
                justify-content: center;
                flex-wrap: wrap;
                gap: var(--spacing-xs);
            }

            .user-menu .btn {
                min-width: 70px;
                font-size: var(--font-size-sm);
                padding: var(--spacing-sm) var(--spacing-md);
            }

            /* 移动端模态框优化 */
            .modal-dialog {
                width: 95%;
                margin: var(--spacing-sm);
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: var(--spacing-md);
            }

            .modal-title {
                font-size: var(--font-size-base);
            }

            .modal-body .form-label {
                font-size: var(--font-size-xs);
            }

            .modal-footer {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .modal-footer .btn {
                width: 100%;
                min-width: auto;
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            .user-menu .btn {
                background-color: rgba(255, 255, 255, 0.3);
                border: 3px solid rgba(255, 255, 255, 0.8);
                color: var(--white);
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }

            .user-menu .btn:hover {
                background-color: rgba(255, 255, 255, 0.5);
                border-color: var(--white);
            }

            .modal-body .form-control {
                border: 3px solid var(--gray-400);
            }

            .modal-body .form-control:focus {
                border-color: var(--primary-color);
                border-width: 3px;
            }

            .modal-title,
            .modal-body .form-label {
                color: var(--gray-900);
                font-weight: 700;
            }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            .modal-content {
                background-color: var(--gray-800);
                color: var(--white);
            }

            .modal-title,
            .modal-body .form-label {
                color: var(--white);
            }

            .modal-body .form-control {
                background-color: var(--gray-700);
                border-color: var(--gray-600);
                color: var(--white);
            }

            .modal-body .form-control:focus {
                background-color: var(--gray-700);
                border-color: var(--primary-color);
                color: var(--white);
            }

            .modal-body .form-control::placeholder {
                color: var(--gray-400);
            }

            #passwordMessage .alert-danger,
            #usernameMessage .alert-danger {
                background-color: rgba(220, 53, 69, 0.2);
                border-color: var(--danger-color);
                color: #ff6b6b;
            }

            #passwordMessage .alert-success,
            #usernameMessage .alert-success {
                background-color: rgba(40, 167, 69, 0.2);
                border-color: var(--success-color);
                color: #51cf66;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .user-menu .btn {
                min-height: 44px;
                padding: var(--spacing-md) var(--spacing-lg);
            }

            .modal-body .form-control {
                min-height: 44px;
                padding: var(--spacing-md);
            }

            .modal-footer .btn {
                min-height: 44px;
                padding: var(--spacing-md) var(--spacing-lg);
            }
        }
    </style>
</head>
<body class="app-layout flex flex-col">
    <!-- 应用头部 -->
    <header class="app-header flex items-center justify-between p-lg">
        <h1 class="text-xl font-semibold m-0">终端管理系统</h1>
        <div class="user-menu flex">
            <button class="btn btn-sm" id="changePasswordBtn"
                    title="修改当前账户密码"
                    aria-label="修改密码">
                🔒 修改密码
            </button>
            <button class="btn btn-sm" id="changeUsernameBtn"
                    title="修改当前用户名"
                    aria-label="修改账号">
                👤 修改账号
            </button>
            <button class="btn btn-sm btn-danger" id="logoutBtn"
                    title="退出当前登录会话"
                    aria-label="退出登录">
                🚪 退出登录
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="flex flex-1 overflow-hidden">
        <!-- 侧边栏 -->
        <aside class="app-sidebar">
            <nav class="sidebar-nav">
                <ul class="m-0 p-0">
                    <li><a href="#" class="nav-link active menu-item" data-page="terminal">📱 终端界面</a></li>
                    <li><a href="#" class="nav-link menu-item" data-page="config">⚙️ 配置设置</a></li>
                </ul>
            </nav>
        </aside>

        <!-- 内容区域 -->
        <main class="main-content-area flex-1 flex flex-col overflow-hidden">
            <div class="iframe-container">
                <!-- 加载指示器 -->
                <div id="iframe-loading" class="iframe-loading">
                    <div class="spinner spinner-lg"></div>
                    <div class="text-muted">正在加载页面...</div>
                </div>

                <iframe id="terminal-page" class="content-frame"
                        src="terminal.html"
                        frameborder="0"
                        scrolling="auto"
                        allowfullscreen
                        loading="lazy"
                        onload="hideIframeLoading()"
                        onerror="showIframeError()"></iframe>
                <iframe id="config-page" class="content-frame hidden"
                        src="config.html"
                        frameborder="0"
                        scrolling="auto"
                        allowfullscreen
                        loading="lazy"
                        onload="hideIframeLoading()"
                        onerror="showIframeError()"></iframe>
            </div>
        </main>
    </div>

<!-- 修改密码弹窗 -->
<div id="passwordModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">🔒 修改密码</h3>
                <button type="button" class="modal-close"
                        aria-label="关闭修改密码对话框"
                        title="关闭">&times;</button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label" for="oldPassword">🔑 旧密码</label>
                        <input type="password" class="form-control" id="oldPassword" name="oldPassword"
                               placeholder="请输入当前密码" required
                               aria-describedby="oldPasswordHelp">
                        <small id="oldPasswordHelp" class="form-text">请输入您当前使用的密码</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="newPassword">🆕 新密码</label>
                        <input type="password" class="form-control" id="newPassword" name="newPassword"
                               placeholder="请输入新密码" required
                               aria-describedby="newPasswordHelp">
                        <small id="newPasswordHelp" class="form-text">密码长度建议6位以上</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="confirmPassword">✅ 确认新密码</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                               placeholder="请再次输入新密码" required
                               aria-describedby="confirmPasswordHelp">
                        <small id="confirmPasswordHelp" class="form-text">请再次输入新密码以确认</small>
                    </div>
                    <div id="passwordMessage"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelPasswordBtn">取消</button>
                    <button type="submit" class="btn btn-primary">确认修改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 修改用户名弹窗 -->
<div id="usernameModal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">👤 修改账号</h3>
                <button type="button" class="modal-close"
                        aria-label="关闭修改账号对话框"
                        title="关闭">&times;</button>
            </div>
            <form id="changeUsernameForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label" for="currentUsername">👤 当前用户名</label>
                        <input type="text" class="form-control" id="currentUsername" name="currentUsername"
                               placeholder="请输入当前用户名" required
                               aria-describedby="currentUsernameHelp">
                        <small id="currentUsernameHelp" class="form-text">请输入您当前的用户名</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="newUsername">🆕 新用户名</label>
                        <input type="text" class="form-control" id="newUsername" name="newUsername"
                               placeholder="请输入新用户名" required
                               aria-describedby="newUsernameHelp">
                        <small id="newUsernameHelp" class="form-text">用户名建议使用字母、数字或下划线</small>
                    </div>
                    <div id="usernameMessage"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelUsernameBtn">取消</button>
                    <button type="submit" class="btn btn-primary">确认修改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 脚本引入 -->
<script src="api.js"></script>
<script src="utils.js"></script>
<script>
    // 检查登录状态
    if (!apiClient.checkAuth()) {
        window.location.href = 'login.html';
    }

    // 移动端 iframe 优化
    function optimizeIframeForMobile() {
        const iframes = document.querySelectorAll('.content-frame');
        const isMobile = window.innerWidth <= 768;

        iframes.forEach(iframe => {
            if (isMobile) {
                // 移动端优化
                iframe.style.position = 'absolute';
                iframe.style.top = '0';
                iframe.style.left = '0';
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = 'none';
                iframe.style.overflow = 'auto';

                // 确保 iframe 内容可以滚动
                iframe.setAttribute('scrolling', 'yes');
            } else {
                // 桌面端优化
                iframe.style.position = 'absolute';
                iframe.style.top = '0';
                iframe.style.left = '0';
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = 'none';
            }
        });
    }

    // 页面加载完成后优化 iframe
    document.addEventListener('DOMContentLoaded', function() {
        optimizeIframeForMobile();

        // 监听窗口大小变化
        window.addEventListener('resize', optimizeIframeForMobile);

        // 监听设备方向变化
        window.addEventListener('orientationchange', function() {
            setTimeout(optimizeIframeForMobile, 100);
        });
    });

    // 修复移动端视口高度问题
    function fixMobileViewport() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);

        // 更新应用布局高度
        const appLayout = document.querySelector('.app-layout');
        if (appLayout && window.innerWidth <= 768) {
            appLayout.style.height = `${window.innerHeight}px`;
        }
    }

    // 初始化视口修复
    fixMobileViewport();
    window.addEventListener('resize', fixMobileViewport);
    window.addEventListener('orientationchange', function() {
        setTimeout(fixMobileViewport, 100);
    });

    // iframe 加载状态管理
    function hideIframeLoading() {
        const loadingElement = document.getElementById('iframe-loading');
        if (loadingElement) {
            loadingElement.classList.add('hidden');
        }
    }

    function showIframeLoading() {
        const loadingElement = document.getElementById('iframe-loading');
        if (loadingElement) {
            loadingElement.classList.remove('hidden');
            loadingElement.innerHTML = `
                <div class="spinner spinner-lg"></div>
                <div class="text-muted">正在加载页面...</div>
            `;
        }
    }

    function showIframeError() {
        const loadingElement = document.getElementById('iframe-loading');
        if (loadingElement) {
            loadingElement.classList.remove('hidden');
            loadingElement.innerHTML = `
                <div class="text-danger text-center">
                    <div class="text-xl mb-md">❌</div>
                    <div>页面加载失败</div>
                    <button class="btn btn-sm btn-primary mt-md" onclick="reloadCurrentIframe()">
                        重新加载
                    </button>
                </div>
            `;
        }
    }

    function reloadCurrentIframe() {
        const activeFrame = document.querySelector('.content-frame:not(.hidden)');
        if (activeFrame) {
            showIframeLoading();
            activeFrame.src = activeFrame.src;
        }
    }

    // 页面切换功能
    document.querySelectorAll('.menu-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // 更新活动菜单项
            document.querySelectorAll('.menu-item').forEach(menuItem => {
                menuItem.classList.remove('active');
            });
            this.classList.add('active');

            // 显示对应页面
            const page = this.getAttribute('data-page');
            const targetFrame = document.getElementById(page + '-page');

            // 隐藏所有 iframe
            document.querySelectorAll('.content-frame').forEach(frame => {
                frame.classList.add('hidden');
            });

            // 显示目标 iframe
            if (targetFrame) {
                targetFrame.classList.remove('hidden');

                // 检查 iframe 是否已加载
                if (!targetFrame.contentDocument || targetFrame.contentDocument.readyState !== 'complete') {
                    showIframeLoading();
                } else {
                    hideIframeLoading();
                }
            }
        });
    });

    // 退出登录功能
    document.getElementById('logoutBtn').addEventListener('click', async function() {
        try {
            showLoading('#logoutBtn', '退出中...');
            await apiClient.logout();
            localStorage.removeItem('sessionId');
            window.location.href = 'login.html';
        } catch (error) {
            hideLoading('#logoutBtn');
            showMessage('登出失败: ' + error.message, 'danger');
        }
    });

    // 模态框事件处理
    document.getElementById('changePasswordBtn').addEventListener('click', () => {
        showModal('#passwordModal');
        document.getElementById('changePasswordForm').reset();
        document.getElementById('passwordMessage').innerHTML = '';
    });

    document.getElementById('changeUsernameBtn').addEventListener('click', () => {
        showModal('#usernameModal');
        document.getElementById('changeUsernameForm').reset();
        document.getElementById('usernameMessage').innerHTML = '';
    });

    // 取消按钮
    document.getElementById('cancelPasswordBtn').addEventListener('click', () => {
        hideModal('#passwordModal');
    });

    document.getElementById('cancelUsernameBtn').addEventListener('click', () => {
        hideModal('#usernameModal');
    });

    // 修改密码功能
    document.getElementById('changePasswordForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const oldPassword = document.getElementById('oldPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const messageDiv = document.getElementById('passwordMessage');

        // 检查新密码和确认密码是否一致
        if (newPassword !== confirmPassword) {
            messageDiv.innerHTML = '<div class="alert alert-danger">新密码和确认密码不一致</div>';
            return;
        }

        const submitBtn = e.target.querySelector('button[type="submit"]');

        try {
            showLoading(submitBtn, '修改中...');
            const data = await apiClient.changePassword(oldPassword, newPassword);

            if (data) {
                showMessage('密码修改成功', 'success');
                hideModal('#passwordModal');
                document.getElementById('changePasswordForm').reset();
                messageDiv.innerHTML = '';
            } else {
                messageDiv.innerHTML = '<div class="alert alert-danger">旧密码错误</div>';
            }
        } catch (error) {
            messageDiv.innerHTML = '<div class="alert alert-danger">密码修改失败</div>';
        } finally {
            hideLoading(submitBtn);
        }
    });

    // 修改用户名功能
    document.getElementById('changeUsernameForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const currentUsername = document.getElementById('currentUsername').value;
        const newUsername = document.getElementById('newUsername').value;
        const messageDiv = document.getElementById('usernameMessage');
        const submitBtn = e.target.querySelector('button[type="submit"]');

        try {
            showLoading(submitBtn, '修改中...');
            const data = await apiClient.changeUsername(currentUsername, newUsername);

            if (data) {
                showMessage('用户名修改成功', 'success');
                hideModal('#usernameModal');
                document.getElementById('changeUsernameForm').reset();
                messageDiv.innerHTML = '';
            } else {
                messageDiv.innerHTML = '<div class="alert alert-danger">当前用户名错误</div>';
            }
        } catch (error) {
            messageDiv.innerHTML = '<div class="alert alert-danger">用户名修改失败</div>';
        } finally {
            hideLoading(submitBtn);
        }
    });


</script>

</body>
</html>
